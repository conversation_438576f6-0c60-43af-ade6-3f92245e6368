name: Linux Release
permissions:
  contents: write

on:
  push:
    tags:
      - "*"
      - "!*-alpha*"
jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 22.4.1
      - name: Build Frontend
        env:
          CI: ""
        run: |
          cd web
          yarn install
          VITE_APP_VERSION=$(git describe --tags) yarn run build
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: ">=1.18.0"
      - name: Build Backend (amd64)
        run: |
          go mod download
          go build -ldflags "-s -w -X 'one-api/common/config.Version=$(git describe --tags)' -extldflags '-static'" -o one-api

      - name: Build Backend (arm64)
        run: |
          sudo rm /etc/apt/sources.list.d/microsoft-prod.list
          sudo apt-get update
          sudo apt-get install gcc-aarch64-linux-gnu
          CC=aarch64-linux-gnu-gcc CGO_ENABLED=1 GOOS=linux GOARCH=arm64 go build -ldflags "-s -w -X 'one-api/common/config.Version=$(git describe --tags)' -extldflags '-static'" -o one-api-arm64

      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            one-api
            one-api-arm64
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GT_Token }}
