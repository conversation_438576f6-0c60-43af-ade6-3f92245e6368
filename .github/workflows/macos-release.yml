name: macOS Release
permissions:
  contents: write

on:
  push:
    tags:
      - "*"
      - "!*-alpha*"
jobs:
  release:
    runs-on: macos-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - uses: actions/setup-node@v3
        with:
          node-version: 22.4.1
      - name: Build Frontend
        env:
          CI: ""
        run: |
          cd web
          yarn install
          VITE_APP_VERSION=$(git describe --tags) yarn run build
          cd ..
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: ">=1.18.0"
      - name: Build Backend
        run: |
          go mod download
          go build -ldflags "-s -w -X 'one-api/common/config.Version=$(git describe --tags)'" -o one-api-macos
      - name: Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: one-api-macos
          draft: true
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GT_Token }}
