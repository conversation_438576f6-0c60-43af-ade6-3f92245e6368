# 服务器设置
port: 3000 # 服务端口
gin_mode: "release" # gin 模式，可选值为 "release" 或 "debug"，默认为 "release"。
log_level: "info" # 日志级别，可选值为 "debug"、"info"、"warn"、"error"、"fatal"、"panic"，默认为 "info"。
log_dir: "./logs" # 日志目录
session_secret: "" # 会话密钥，未设置则使用随机值。
disable_token_encoders: false # 是否禁用 token 编码器计算tokens。启用后 内存占用可减少 40MB 左右，但是stream模式下tokens计算不准确
trusted_header: "" # 可信头部，"CF-Connecting-IP" 用于 Cloudflare，"X-Appengine-Remote-Addr" 用于 Google App Engine，未设置则不使用。 可以解决一些代理问题，如获取用户真实IP
language: "zh_CN" #国际化默认语言 目前支持`zh_CN`/`en_US`/ja_JP`/`zh_HK`
favicon: "" # 设置favicon路径,可以是ico的URL，也可以是文件路径。前后端分离部署的请直接修改前端的favicon.ico文件
logs:
  filename: "one-hub.log" # 日志文件名
  max_size: 100 # 日志文件最大大小，单位为 MB，默认为 100。
  max_backups: 10 # 日志文件最大备份数量，默认为 10。
  max_age: 7 # 日志文件最大保存天数，默认为 7。
  compress: false # 是否启用日志压缩，默认为 false

# 数据库设置
sql_dsn: "" # 设置之后将使用指定数据库而非 SQLite，请使用 MySQL 或 PostgreSQL
sqlite_path: "one-api.db" # sqlite 数据库文件路径
sqlite_busy_timeout: 3000 # sqlite 数据库繁忙超时时间，单位为毫秒，默认为 3000。
redis_conn_string: "" # 设置之后将使用指定 Redis 作为缓存，格式为 "redis://default:redispw@localhost:49153"，未设置则不使用 Redis。
redis_db: 0 # redis 数据库，未设置则不使用 Redis。

memory_cache_enabled: false # 是否启用内存缓存，启用后将缓存部分数据，减少数据库查询次数。
sync_frequency: 600 # 在启用缓存的情况下与数据库同步配置的频率，单位为秒，默认为 600 秒
node_type: "master" # 节点类型，可选值为 "master" 或 "slave"，默认为 "master"。
frontend_base_url: "" # 设置之后将重定向页面请求到指定的地址，仅限从服务器设置。
polling_interval: 0 # 批量更新渠道余额以及测试可用性时的请求间隔，单位为秒，默认无间隔。
batch_update_interval: 5 # 批量更新聚合的时间间隔，单位为秒，默认为 5。
batch_update_enabled: false # 启用数据库批量更新聚合，会导致用户额度的更新存在一定的延迟可选值为 true 和 false，未设置则默认为 false
auto_price_updates: true # 启用自动更新价格，可选值为 true 和 false，默认为 true

# 令牌设置
user_token_secret: "" # 用户令牌密钥, 请设置至少32位的随机字符串，修改后用户令牌将无法验证，例如：vWVmFxp5YIOXuHhEod8jBcqiw0zKP2fk
hashids_salt: "" # sqids alphabet参数，可空，如果不设置则使用默认字表, 如果配置则需要保证字符串中文字不重复，修改后用户令牌将无法验证，

# 全局设置
global:
  api_rate_limit: 180 # 全局 API 速率限制（除中继请求外），单 ip 三分钟内的最大请求数，默认为 180。
  web_rate_limit: 100 # 全局 Web 速率限制，单 ip 三分钟内的最大请求数，默认为 100。

# 频道更新设置
channel:
  update_frequency: 0 # 设置之后将定期更新渠道余额，单位为分钟，未设置则不进行更新。
  test_frequency: 0 # 设置之后将定期检查渠道，单位为分钟，未设置则不进行检查

# 连接设置
relay_timeout: 0 # 中继请求超时时间，单位为秒，默认为 0。
connect_timeout: 5 # 连接超时时间，单位为秒，默认为 5。

# 默认程序启动时会联网下载一些通用的Token的编码，如：gpt-3.5-turbo，在一些网络环境不稳定，或者离线情况，可能会导致启动有问题，可以配置此目录缓存数据，可迁移到离线环境。
tiktoken_cache_dir: ""
# 目前该配置作用与 TIKTOKEN_CACHE_DIR 一致，但是优先级没有它高。
data_gym_cache_dir: ""

# Telegram设置
tg:
  bot_api_key: "" # 你的 Telegram bot 的 API 密钥
  webhook_secret: "" # 你的 webhook 密钥。你可以自定义这个密钥。如果设置了这个密钥，将使用webhook的方式接收消息，否则使用轮询（Polling）的方式。
  http_proxy: "" # 代理设置，格式为 "http://127.0.0.1:1080" 或 "socks5://"，未设置则不使用代理。
notify: # 通知设置, 配置了几个通知方式，就会同时发送几次通知 如果不需要通知，可以删除这个配置
  email: # 邮件通知 (具体stmp配置在后台设置)
    disable: false # 是否禁用邮件通知
    smtp_to: "" # 收件人地址 (可空，如果为空则使用超级管理员邮箱)
  wecom: # 企业微信机器人通知
    disable: false # 是否禁用企业微信机器人通知
    webhook: "" # 完整的调用地址
  dingTalk: # 钉钉机器人通知
    token: "" # webhook 地址最后一串字符
    secret: "" # 密钥 (secret/keyWord 二选一)
    keyWord: "" # 关键字 (secret/keyWord 二选一)
  lark: # 飞书机器人通知
    token: "" # webhook 地址最后一串字符
    secret: "" # 密钥 (secret/keyWord 二选一)
    keyWord: "" # 关键字 (secret/keyWord 二选一)
  pushdeer: # pushdeer 通知
    url: "https://api2.pushdeer.com" # pushdeer地址 (可空，如果自建需填写)
    pushkey: "" # pushkey
  telegram: # Telegram 通知
    bot_api_key: "" # 你的 Telegram bot 的 API 密钥
    chat_id: "" # 你的 Telegram chat_id
    http_proxy: "" # 代理设置，格式为 "http://127.0.0.1:1080" 或 "socks5://"，未设置则不使用代理。
storage: # 存储设置 (可选,主要用于图片生成，有些供应商不提供url，只能返回base64图片，设置后可以正常返回url格式的图片生成)
  smms: # sm.ms 图床设置
    secret: "" # 你的 sm.ms API 密钥
  imgur:
    client_id: "" # 你的 imgur client_id
  alioss: # 阿里云OSS对象存储
    endpoint: "" # Endpoint（地域节点）,比如oss-cn-beijing.aliyuncs.com
    bucketName: "" # Bucket名称，比如zerodeng-superai
    accessKeyId: "" # 阿里授权KEY,在阿里云后台用户RAM控制部分获取
    accessKeySecret: "" # 阿里授权SECRET,在阿里云后台用户RAM控制部分获取
  s3: # AwsS3协议
    endpoint: "" # Endpoint（地域节点）,比如https://xxxxxx.r2.cloudflarestorage.com
    cdnurl: "" # 公共访问域名，比如https://pub-xxxxx.r2.dev，如果不配置则使用endpoint
    bucketName: "" # Bucket名称，比如zerodeng-superai
    accessKeyId: "" # accessKeyId
    accessKeySecret: "" # accessKeySecret
    expirationDays: 3

metrics:
  user: "" # metrics 用户名
  password: "" # metrics 密码

search:
  searxng:
    url: "" # searxng 地址 关键词请用{query}， 例如 "http://127.0.0.1:8080/search?category_general=1&safesearch=2&q={query}&format=json&engines=bing,google"
  tavily:
    key: "" # tavily 密钥
