version: "3.4"

services:
  one-hub:
    image: martialbe/one-api:latest
    container_name: one-hub
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - ./data:/data
    # 添加网络配置以连接外部服务
    networks:
      - default
      - 1panel-network
    environment:
      # 外部 MySQL 数据库连接配置
      - SQL_DSN=one_api_user:ryTNMscMaNcA4Hz4@tcp(1Panel-mysql-NtH3:3306)/one_api_db
      # 外部 Redis 连接配置
      - REDIS_CONN_STRING=redis://default:redis_nA2KKY@1Panel-redis-HPT0:6379
      - SESSION_SECRET=random_string # 修改为随机字符串
      - USER_TOKEN_SECRET=random_string # 修改为随机字符串,32位以上
      - TZ=Asia/Shanghai
      # - HASHIDS_SALT=random_string # 可空，建议设置，字符串元素不能重复
    #      - NODE_TYPE=slave  # 多机部署时从节点取消注释该行
    #      - SYNC_FREQUENCY=60  # 需要定期从数据库加载数据时取消注释该行
    #      - FRONTEND_BASE_URL=https://openai.justsong.cn  # 多机部署时从节点取消注释该行
    # 移除内置服务依赖，使用外部 Redis 和 MySQL
    # depends_on:
    #   - redis
    #   - db
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'",
        ]
      interval: 30s
      timeout: 10s
      retries: 3

  # 注释掉内置 Redis 服务，使用外部 Redis (1Panel-redis-HPT0)
  # redis:
  #   image: redis:latest
  #   container_name: redis
  #   restart: always

  # 注释掉内置 MySQL 服务，使用外部 MySQL (1Panel-mysql-NtH3)
  # db:
  #   image: mysql:8.2.0
  #   restart: always
  #   container_name: mysql
  #   volumes:
  #     - ./data/mysql:/var/lib/mysql # 挂载目录，持久化存储
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     TZ: Asia/Shanghai # 设置时区
  #     MYSQL_ROOT_PASSWORD: "OneAPI@justsong" # 设置 root 用户的密码
  #     MYSQL_USER: oneapi # 创建专用用户
  #     MYSQL_PASSWORD: "123456" # 设置专用用户密码
  #     MYSQL_DATABASE: one-api # 自动创建数据库

# 网络配置：连接到 1Panel 创建的外部服务网络
networks:
  1panel-network:
    external: true
    name: 1panel-network  # 1Panel 默认网络名称，如果不同请修改
