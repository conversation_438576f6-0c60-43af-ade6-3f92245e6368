export const CHANNEL_OPTIONS = {
  1: {
    key: 1,
    text: 'OpenAI',
    value: 1,
    color: 'primary',
    url: 'https://platform.openai.com/usage'
  },
  14: {
    key: 14,
    text: 'Anthropic Claude',
    value: 14,
    color: 'info',
    url: 'https://console.anthropic.com/'
  },
  3: {
    key: 3,
    text: 'Azure OpenAI',
    value: 3,
    color: 'orange',
    url: 'https://oai.azure.com/'
  },
  11: {
    key: 11,
    text: 'Google PaLM2',
    value: 11,
    color: 'orange',
    url: 'https://aistudio.google.com/'
  },
  25: {
    key: 25,
    text: 'Google Gemini',
    value: 25,
    color: 'orange',
    url: 'https://aistudio.google.com/'
  },
  15: {
    key: 15,
    text: '百度文心千帆',
    value: 15,
    color: 'default',
    url: 'https://console.bce.baidu.com/qianfan/overview'
  },
  17: {
    key: 17,
    text: '阿里通义千问',
    value: 17,
    color: 'default',
    url: 'https://dashscope.console.aliyun.com/overview'
  },
  18: {
    key: 18,
    text: '讯飞星火认知',
    value: 18,
    color: 'default',
    url: 'https://console.xfyun.cn/'
  },
  16: {
    key: 16,
    text: '智谱 ChatGLM',
    value: 16,
    color: 'default',
    url: 'https://open.bigmodel.cn/overview'
  },
  19: {
    key: 19,
    text: '360 智脑',
    value: 19,
    color: 'default',
    url: 'https://ai.360.com/open'
  },
  23: {
    key: 23,
    text: '腾讯混元(旧)',
    value: 23,
    color: 'default',
    url: 'https://cloud.tencent.com/product/hunyuan'
  },
  26: {
    key: 26,
    text: '百川',
    value: 26,
    color: 'orange',
    url: 'https://platform.baichuan-ai.com/console/apikey'
  },
  27: {
    key: 27,
    text: 'MiniMax',
    value: 27,
    color: 'orange',
    url: 'https://www.minimaxi.com/user-center/basic-information'
  },
  28: {
    key: 28,
    text: 'Deepseek',
    value: 28,
    color: 'default',
    url: 'https://platform.deepseek.com/usage'
  },
  29: {
    key: 29,
    text: 'Moonshot',
    value: 29,
    color: 'default',
    url: 'https://platform.moonshot.cn/console/info'
  },
  30: {
    key: 30,
    text: 'Mistral',
    value: 30,
    color: 'orange',
    url: 'https://console.mistral.ai/'
  },
  31: {
    key: 31,
    text: 'Groq',
    value: 31,
    color: 'primary',
    url: 'https://console.groq.com/keys'
  },
  32: {
    key: 32,
    text: 'Amazon Bedrock',
    value: 32,
    color: 'orange',
    url: 'https://console.aws.amazon.com/bedrock/home'
  },
  33: {
    key: 33,
    text: '零一万物',
    value: 33,
    color: 'primary',
    url: 'https://platform.lingyiwanwu.com/details'
  },
  34: {
    key: 34,
    text: 'Midjourney',
    value: 34,
    color: 'orange',
    url: ''
  },
  35: {
    key: 35,
    text: 'Cloudflare AI',
    value: 35,
    color: 'orange',
    url: 'https://ai.cloudflare.com/'
  },
  36: {
    key: 36,
    text: 'Cohere',
    value: 36,
    color: 'default',
    url: 'https://cohere.com/'
  },
  37: {
    key: 37,
    text: 'Stability AI',
    value: 37,
    color: 'default',
    url: 'https://platform.stability.ai/account/credits'
  },
  38: {
    key: 38,
    text: 'Coze',
    value: 38,
    color: 'primary',
    url: 'https://www.coze.com/open/docs/chat?_lang=zh'
  },
  39: {
    key: 39,
    text: 'Ollama',
    value: 39,
    color: 'orange',
    url: ''
  },
  40: {
    key: 40,
    text: '腾讯混元',
    value: 40,
    color: 'default',
    url: 'https://cloud.tencent.com/product/hunyuan'
  },
  41: {
    key: 41,
    text: 'Suno',
    value: 41,
    color: 'default'
  },
  42: {
    key: 42,
    text: 'VertexAI',
    value: 42,
    color: 'orange',
    url: 'https://console.cloud.google.com/'
  },
  45: {
    key: 45,
    text: 'Siliconflow',
    value: 45,
    color: 'orange',
    url: 'https://siliconflow.cn/'
  },
  47: {
    key: 47,
    text: 'Jina',
    value: 47,
    color: 'orange',
    url: 'https://jina.ai/'
  },
  24: {
    key: 24,
    text: 'Azure Speech',
    value: 24,
    color: 'orange',
    url: 'https://portal.azure.com/'
  },
  49: {
    key: 49,
    text: 'Github',
    value: 49,
    color: 'default',
    url: 'https://github.com/marketplace/models'
  },
  51: {
    key: 51,
    text: 'RecraftAI',
    value: 51,
    color: 'orange',
    url: ''
  },
  52: {
    key: 52,
    text: 'Replicate',
    value: 52,
    color: 'default',
    url: ''
  },
  53: {
    key: 53,
    text: 'Kling',
    value: 53,
    color: 'warning',
    url: 'https://api.kling.ai'
  },
  8: {
    key: 8,
    text: '自定义渠道',
    value: 8,
    color: 'primary',
    url: ''
  }
};
