{"dashboard": "仪表盘", "analytics": "分析", "playground": "聊天", "price": "价格", "channel": "渠道", "token": "令牌", "log": "日志", "redemption": "兑换", "topup": "充值", "user": "用户", "user_group": "用户分组", "profile": "个人设置", "pricing": "模型价格", "model_price": "可用模型", "payment": "支付", "setting": "设置", "task": "异步任务", "operation": "运营", "paySetting": "支付设置", "menu": {"home": "首页", "about": "关于", "login": "登录", "signup": "注册", "signout": "注销", "console": "控制台", "error": "错误", "unknownVersion": "未知版本", "welcomeBack": "欢迎回来"}, "registerForm": {"enterEmail": "请输入邮箱", "turnstileError": "请稍后几秒重试，Turnstile 正在检查用户环境！", "usernameRequired": "用户名是必填项", "passwordRequired": "密码是必填项", "confirmPasswordRequired": "确认密码是必填项", "passwordsNotMatch": "两次输入的密码不一致", "validEmailRequired": "必须是有效的Email地址", "emailRequired": "Email是必填项", "verificationCodeRequired": "验证码是必填项", "resendCode": "重新发送({{countdown}})", "getCode": "获取验证码", "verificationInfo": "请稍后几秒重试，Turnstile 正在检查用户环境！", "restSendEmail": "重置邮件发送成功，请检查邮箱！"}, "registerPage": {"alreadyHaveAccount": "已经有帐号了?点击登录"}, "dashboard_index": {"model_price": "当前可用模型", "today_requests": "今日请求", "today_consumption": "今日消费", "today_tokens": "今日Token", "no_data": "无数据", "statistics": "统计", "no_data_available": "暂无数据", "model_name": "模型名称", "other_models": "其他模型", "balance": "余额", "used": "已使用", "calls": "调用次数", "unknown": "未知"}, "analytics_index": {"startTime": "开始时间", "endTime": "结束时间", "consumptionStatistics": "消费统计", "tokensStatistics": "Tokens统计", "averageLatency": "平均延迟", "requestsCount": "请求数", "redemptionStatistics": "兑换统计", "registrationStatistics": "注册统计", "totalUserSpending": "用户总消费金额", "totalUserBalance": "用户总余额", "totalUsers": "用户总数", "directRegistration": "直接注册", "invitationRegistration": "邀请注册", "channelCount": "渠道数量", "active": "正常", "disabled": "禁用", "testDisabled": "测试禁用", "redeemCodeIssued": "兑换码发行量", "used": "已使用", "unused": "未使用"}, "channel_index": {"channelList": "渠道列表", "channelTags": "渠道标签", "channel": "渠道", "newChannel": "新建渠道", "batchProcessing": "批量处理", "priorityWeightExplanation": "优先级/权重解释：", "description1": "1. 优先级越大，越优先使用；(只有该优先级下的节点都冻结或者禁用了，才会使用低优先级的节点)", "description2": "2. 相同优先级下：根据权重进行负载均衡(加权随机)", "description3": "3. 如果在设置-通用设置中设置了“重试次数”和“重试间隔”，则会在失败后重试。", "description4": "4. 重试逻辑：1）先在高优先级中的节点重试，如果高优先级中的节点都冻结了，才会在低优先级中的节点重试。2）如果设置了“重试间隔”，则某一渠道失败后，会冻结一段时间，所有人都不会再使用这个渠道，直到冻结时间结束。3）重试次数用完后，直接结束。", "refreshClearSearchConditions": "刷新/清除搜索条件", "search": "搜索", "testAllChannels": "测试所有渠道", "updateEnabledBalance": "更新启用余额", "deleteDisabledChannels": "删除禁用渠道", "name": "名称", "group": "分组", "tags": "标签", "type": "类型", "status": "状态", "responseTime": "响应时间", "usedBalance": "已使用/余额", "priority": "优先级", "weight": "权重", "actions": "操作", "supplier": "供应商", "model": "模型", "modelName": "模型名称", "channelName": "渠道名称", "testModel": "测试模型", "otherParameters": "其他参数", "channelType": "渠道类型", "all": "全部", "enabled": "启用", "disabled": "禁用", "speedTestDisabled": "测速禁用", "filterTags": "过滤标签", "onlyTags": "仅显示标签", "batchAzureAPISuccess": "成功更新 {{count}} 条数据", "inputAPIVersion": "请输入api版本号", "selectAll": "全选", "unselectAll": "反全选", "replaceValue": "替换值", "batchDeleteSuccess": "成功删除 {{count}} 条数据", "batchDeleteTip": "如果渠道只有一个模型的，将不会显示，请手动去列表删除渠道", "batchDeleteModel": "请输入完整模型名称", "AzureApiVersion": "Azure 版本号", "batchDelete": "批量删除模型"}, "channel_edit": {"customModelTip": "自定义：点击或回车输入", "modelListError": "获取模型列表失败", "editSuccess": "更新成功!", "addSuccess": "创建成功！", "batchAdd": "批量添加", "isEnable": "是否启用", "batchBaseurlTip": "，一行一个,顺序对应下面的key，如果对应不上则默认使用第一个", "batchKeytip": "，一行一个密钥", "inputAllModel": "填入所有模型", "inputChannelModel": "填入渠道支持模型", "requiredName": "名称 不能为空", "requiredChannel": "渠道 不能为空", "requiredKey": "密钥 不能为空", "requiredModels": "模型 不能为空", "requiredGroup": "用户组 不能为空", "requiredBaseUrl": "渠道API地址 不能为空", "validJson": "必须是有效的JSON字符串", "modelMappingKey": "用户请求模型", "modelMappingValue": "实际转发模型", "addModelMapping": "添加模型映射", "addModelMappingByJson": "添加模型映射(JSON)", "jsonInputLabel": "JSON对象，key为请求模型，value为实际转发模型", "invalidJson": "无效的JSON", "modelHeaderKey": "自定义Header Key", "modelHeaderValue": "自定义Header Value", "addModelHeader": "添加自定义Header", "collapse": "收起", "expand": "展开", "copyModels": "复制模型"}, "token_index": {"token": "令牌", "createToken": "新建令牌", "replaceApiAddress1": "将OpenAI API基础地址https://api.openai.com替换为", "replaceApiAddress2": "，复制下面的密钥即可使用。", "searchTokenName": "搜索令牌的名称...", "refresh": "刷新", "name": "名称", "status": "状态", "usedQuota": "已用额度", "remainingQuota": "剩余额度", "createdTime": "创建时间", "expiryTime": "过期时间", "actions": "操作", "unlimited": "无限制", "neverExpires": "永不过期", "copy": "复制", "chat": "聊天", "deleteToken": "删除Token", "confirmDeleteToken": "是否删除Token", "close": "关闭", "delete": "删除", "editToken": "编辑令牌", "quotaNote": "注意，令牌的额度仅用于限制令牌本身的最大额度使用量，实际的使用受到账户的剩余额度限制。", "invalidDate": "无效的日期", "quota": "额度", "unlimitedQuota": "无限额度", "enableCache": "是否开启缓存(开启后，将会缓存聊天记录，以减少消费)", "userGroup": "分组", "cancel": "取消", "submit": "提交"}, "logPage": {"title": "日志", "refreshButton": "刷新/清除搜索条件", "searchButton": "搜索", "columnSettings": "列设置", "selectColumns": "选择列", "columnSelectAll": "全选", "timeLabel": "时间", "channelLabel": "渠道", "groupLabel": "分组", "userLabel": "用户", "tokenLabel": "令牌", "typeLabel": "类型", "modelLabel": "模型", "durationLabel": "耗时", "durationTooltip": "t/s：输出Token的数量除以总生成时间，表示生成速度", "inputLabel": "输入", "outputLabel": "输出", "quotaLabel": "额度", "detailLabel": "详情", "searchLogsInfo": "充值记录以及邀请记录请在日志中查询。充值记录请在日志中选择类型【充值】查询；邀请记录请在日志中选择【系统】查询", "inputTextTokens": "输入文本Tokens", "outputTextTokens": "输出文本Tokens", "inputAudioTokens": "输入音频Tokens", "outputAudioTokens": "输出音频Tokens", "cachedTokens": "缓存Tokens", "cachedWriteTokens": "缓存写入Tokens", "cachedReadTokens": "缓存读取Tokens", "totalInputTokens": "计算输入Tokens", "totalOutputTokens": "计算输出Tokens", "sourceIp": "来源IP", "content": {"channel_group": "分组: {{ channel_group }}", "group_discount": "分组折扣: {{ discount }}", "input_price": "输入: ${{ price }} /M", "output_price": "输出: ${{ price }} /M", "times_price": "${{ times }} / 次", "original_input_price": "原输入价格: ${{ price }} /M", "original_output_price": "原输出价格: ${{ price }} /M", "original_times_price": "原价格: ${{ times }} / 次", "calculate_steps": "计算步骤: ", "calculate_steps_tip": "PS：本系统按照积分计算，所有金额均为积分换算而来，1积分=$0.000002，最低消费为1积分，本计算步骤仅供参考，以实际扣费为准", "free": "免费", "old_log": "旧版记录", "illustrate": "* 本条记录为系统升级前所记录的log"}}, "redemptionPage": {"pageTitle": "兑换", "createRedemptionCode": "创建兑换码", "successMessage": "操作成功", "searchPlaceholder": "搜索兑换码...", "refreshButton": "刷新", "headLabels": {"id": "ID", "name": "名称", "status": "状态", "quota": "额度", "createdTime": "创建时间", "redeemedTime": "兑换时间", "action": "操作"}, "unredeemed": "尚未兑换", "del": "删除兑换码", "delTip": "是否删除兑换码"}, "topupPage": {"alertMessage": "充值记录以及邀请记录请在日志中查询。充值记录请在日志中选择类型【充值】查询；邀请记录请在日志中选择【系统】查询"}, "topupCard": {"topup": "充值", "topupsuccess": "充值成功！", "amount": "充值金额", "currentQuota": "当前额度", "onlineTopup": "在线充值", "inputLabel": "兑换码", "inputPlaceholder": "请输入兑换码！", "exchangeButton": {"default": "兑换", "submitting": "提交中"}, "selectPaymentMethod": "请选择支付方式", "amountMinLimit": "金额不能少于", "amountMaxLimit": "金额不能超过 1000000", "positiveIntegerAmount": "请输入正整数金额", "topupAmount": "充值金额", "discountedPrice": "折扣后价格", "fee": "手续费", "actualAmountToPay": "实际支付金额", "adminSetupRequired": "管理员尚未设置充值链接！", "redemptionCodeTopup": "兑换码充值", "noRedemptionCodeText": "还没有兑换码？ 点击获取兑换码：", "getRedemptionCode": "获取兑换码", "exchangeRate": "汇率"}, "inviteCard": {"inviteReward": "邀请奖励", "inviteDescription": "分享您的邀请链接，邀请好友注册，即可获得奖励!", "inviteUrlLabel": "邀请链接", "generateInvite": "点击生成邀请链接", "copyButton": {"copy": "复制", "generate": "生成"}}, "tableToolBar": {"tokenName": "令牌名称", "modelName": "模型名称", "startTime": "起始时间", "endTime": "结束时间", "type": "类型", "channelId": "渠道ID", "channelIdPlaceholder": "渠道ID", "taskId": "任务ID", "taskIdPlaceholder": "任务ID", "username": "用户名称", "sourceIp": "来源IP"}, "midjourneyPage": {"midjourney": "Midjourney", "refreshClearSearch": "刷新/清除搜索条件", "search": "搜索", "taskID": "任务ID", "submitTime": "提交时间", "channel": "渠道", "user": "用户", "type": "类型", "submissionResult": "提交结果", "taskStatus": "任务状态", "progress": "进度", "timeConsuming": "耗时", "resultImage": "结果图片", "prompt": "Prompt", "promptEn": "PromptEn", "failureReason": "失败原因"}, "userPage": {"editUser": "编辑用户", "createUser": "新建用户", "username": "用户名", "usernameRequired": "用户名不能为空", "displayName": "显示名称", "password": "密码", "passwordRequired": "密码不能为空", "quota": "额度", "quotaMin": "额度不能小于 0", "group": "分组", "groupRequired": "用户组不能为空", "saveSuccess": "保存成功！", "cancel": "取消", "submit": "提交", "users": "用户", "searchPlaceholder": "搜索用户的ID，用户名，分组，显示名称，以及邮箱地址...", "refresh": "刷新", "operationSuccess": "操作成功完成！", "id": "ID", "statistics": "统计信息", "userRole": "用户角色", "bind": "绑定", "creationTime": "创建时间", "status": "状态", "action": "操作", "useQuota": "请求次数", "setAdmin": "设为管理员", "cancelAdmin": "取消管理员", "del": "删除用户", "delTip": "是否删除用户", "cUserRole": "普通用户", "adminUserRole": "管理员", "superAdminRole": "超级管理员", "uUserRole": "未知身份", "changeQuota": "增减额度", "changeQuotaHelperText": "这里是增减，不是直接更改用户余额，输入美元，你最多可以减扣 {{quota}}", "changeQuotaNotEmpty": "请填写变更额度", "changeQuotaNotEnough": "不能扣除超过用户余额的额度", "quotaRemark": "备注"}, "profilePage": {"personalInfo": "个人信息", "username": "用户名", "displayName": "显示名称", "password": "密码", "usernameRequired": "用户名不能为空", "usernameMinLength": "用户名不能小于 3 个字符", "passwordMinLength": "密码不能小于 8 个字符", "token": "令牌", "wechatBindSuccess": "微信账户绑定成功！", "updateSuccess": "用户信息更新成功！", "notBound": "未绑定", "inputUsernamePlaceholder": "请输入用户名", "inputPasswordPlaceholder": "请输入密码", "inputDisplayNamePlaceholder": "请输入显示名称", "submit": "提交", "accountBinding": "账号绑定", "bindWechatAccount": "绑定微信账号", "bindGitHubAccount": "绑定Github账号", "bindLarkAccount": "绑定飞书账号", "changeEmail": "更换邮箱", "bindEmail": "绑定邮箱", "telegramBot": "Telegram 机器人", "telegramStep1": "1. 点击下方按钮，将会在 Telegram 中打开机器人，点击 /start 开始。", "telegramStep2": "2. 向机器人发送/bind命令后，输入下方的访问令牌即可绑定。(如果没有生成，请点击下方按钮生成)", "other": "其他", "generateToken": "生成访问令牌", "resetToken": "重置访问令牌", "lark": "飞书", "tokenNotice": "注意，此处生成的令牌用于系统管理，而非用于请求 OpenAI 相关的服务，请知悉。", "yourTokenIs": "你的访问令牌是:", "keepSafe": "请妥善保管。如有泄漏，请立即重置。"}, "pricingPage": {"currencyInfo1": "美元", "currencyInfo2": "：1 === $0.002 / 1K tokens", "currencyInfo3": "人民币", "currencyInfo4": "： 1 === ￥0.014 / 1k tokens", "currencyInfo5": "例如", "currencyInfo6": "：gpt-4 输入： $0.03 / 1K tokens 完成：$0.06 / 1K tokens", "currencyInfo7": "0.03 / 0.002 = 15, 0.06 / 0.002 = 30，即输入倍率为 15，完成倍率为 30", "noPriceModelWarning": "存在未配置价格的模型，请及时配置价格：", "errPricesWarning": "存在供应商类型错误的模型，请及时配置：", "newButton": "新建", "refreshButton": "刷新", "updatePricesButton": "更新价格", "singleOperation": "单条操作", "multipleOperation": "合并操作", "ModelCount": "模型数量"}, "modelpricePage": {"model": "模型名称", "type": "类型", "channelType": "供应商", "availableModels": "可用模型", "group": "分组", "inputMultiplier": "输入价格", "outputMultiplier": "输出价格", "rate": "倍率", "search": "搜索", "tokens": "按量付费", "times": "按次付费", "noneGroup": "当前分组不可用", "other": "其他", "inputAudioTokensRatio": "音频输入倍率", "outputAudioTokensRatio": "音频输出倍率"}, "paymentPage": {"orderList": "订单列表", "gatewaySettings": "网关设置"}, "paymentGatewayPage": {"title": "支付网关", "createPayment": "新建支付", "refreshClear": "刷新/清除搜索条件", "search": "搜索", "tableHeaders": {"id": "ID", "uuid": "UUID", "name": "名称", "type": "类型", "icon": "图标", "fixedFee": "固定手续费", "percentFee": "百分比手续费", "sort": "排序", "enable": "启用", "createdAt": "创建时间", "action": "操作"}}, "orderlogPage": {"title": "日志", "refreshClear": "刷新/清除搜索条件", "search": "搜索", "tableHeaders": {"created_at": "时间", "gateway_id": "支付网关", "user_id": "用户", "trade_no": "订单号", "gateway_no": "网关订单号", "amount": "充值金额", "fee": "手续费", "discount": "优惠金额", "order_amount": "实际支付金额", "quota": "到帐点数", "status": "状态"}, "gatewayIdLabel": "网关ID", "userIdLabel": "用户ID", "tradeNoLabel": "订单号", "gatewayNoLabel": "网关订单号", "startTimeLabel": "起始时间", "endTimeLabel": "结束时间", "statusLabel": "状态", "placeholder": {"gatewayId": "网关ID", "userId": "用户ID", "tradeNo": "订单号", "gatewayNo": "网关订单号"}, "statusOptions": {"status1": "状态1", "status2": "状态2", "status3": "状态3"}}, "setting_index": {"operationSettings": {"title": "运营设置", "generalSettings": {"title": "通用设置", "topUpLink": {"label": "充值链接", "placeholder": "例如发卡网站的购买链接"}, "chatLink": {"label": "聊天链接", "placeholder": "例如 ChatGPT Next Web 的部署地址"}, "quotaPerUnit": {"label": "单位额度", "placeholder": "一单位货币能兑换的额度"}, "retryTimes": {"label": "重试次数", "placeholder": "重试次数"}, "retryCooldownSeconds": {"label": "重试间隔(秒)", "placeholder": "重试间隔(秒)"}, "retryTimeOut": {"label": "重试超时时间(秒)", "placeholder": "重试超时时间(秒)"}, "displayInCurrency": "以货币形式显示额度", "displayTokenStat": "Billing 相关 API 显示令牌额度而非用户额度", "approximateToken": "使用近似的方式估算 token 数以减少计算量", "saveButton": "保存通用设置"}, "otherSettings": {"title": "其他设置", "mjNotify": "Midjourney 允许回调（会泄露服务器ip地址）", "claudeAPIEnabled": "是否开启Claude API", "geminiAPIEnabled": "是否开启Gemini API", "alert": "当用户使用vision模型并提供了图片链接时，我们的服务器需要下载这些图片并计算 tokens。为了在下载图片时保护服务器的 IP 地址不被泄露，可以在下方配置一个代理。这个代理配置使用的是 HTTP 或 SOCKS5 代理。如果你是个人用户，这个配置可以不用理会。代理格式为 http://127.0.0.1:1080 或 socks5://127.0.0.1:1080", "chatImageRequestProxy": {"label": "图片检测代理", "placeholder": "聊天图片检测代理设置，如果不设置可能会泄漏服务器ip"}, "CFWorkerImageUrl": {"label": "Cloudflare Worker 图片代理", "key": "Cloudflare Worker 图片代理key,如果没有配置请忽略", "alert": "这里是Cloudflare Worker的图片代理地址，你可以通过部署https://github.com/MartialBE/get-image-by-cf，来使用，它和图片检测代理可以只设置其中一个。注意有些图片链接可能会拒绝CF的访问导致检测失败。"}, "saveButton": "保存其他设置"}, "logSettings": {"title": "日志设置", "logConsume": "启用日志消费", "logCleanupTime": {"label": "日志清理时间", "placeholder": "日志清理时间"}, "clearLogs": "清理历史日志"}, "monitoringSettings": {"title": "监控设置", "channelDisableThreshold": {"label": "最长响应时间", "placeholder": "单位秒，当运行通道全部测试时，超过此时间将自动禁用通道"}, "quotaRemindThreshold": {"label": "额度提醒阈值", "placeholder": "低于此额度时将发送邮件提醒用户"}, "automaticDisableChannel": "失败时自动禁用通道", "automaticEnableChannel": "成功时自动启用通道", "saveMonitoringSettings": "保存监控设置"}, "quotaSettings": {"title": "额度设置", "quotaForNewUser": {"label": "新用户初始额度", "placeholder": "例如：100"}, "preConsumedQuota": {"label": "请求预扣费额度", "placeholder": "请求结束后多退少补"}, "quotaForInviter": {"label": "邀请新用户奖励额度", "placeholder": "例如：2000"}, "quotaForInvitee": {"label": "新用户使用邀请码奖励额度", "placeholder": "例如：1000"}, "saveQuotaSettings": "保存额度设置"}, "paymentSettings": {"title": "支付设置", "alert": "支付设置： <br />1. 美元汇率：用于计算充值金额的美元金额 <br />2. 最低充值金额（美元）：最低充值金额，单位为美元，填写整数 <br />3. 页面都以美元为单位计算，实际用户支付的货币，按照支付网关设置的货币进行转换 <br />例如： A 网关设置货币为 CNY，用户支付 100 美元，那么实际支付金额为 100 * 美元汇率 <br />B 网关设置货币为 USD，用户支付 100 美元，那么实际支付金额为 100 美元", "usdRate": {"label": "美元汇率", "placeholder": "例如：7.3"}, "minAmount": {"label": "最低充值金额（美元）", "placeholder": "例如：1，那么最低充值金额为1美元，请填写整数"}, "discountInfo": "固定金额充值折扣设置示例： <br />为一个 JSON文本，键为充值金额，值为折扣，比如 &#123;&quot;10&quot;:0.9&#125; 表示充值10美元按照9折计算 <br />计算公式：实际费用=（原始价值*折扣+原始价值*折扣*手续费率）*汇率", "discount": {"label": "固定金额充值折扣", "placeholder": "为一个 JSON 文本，键为充值金额，值为折扣"}, "save": "保存支付设置"}, "rateSettings": {"title": "倍率设置", "groupRatio": {"label": "分组倍率", "placeholder": "为一个 JSON 文本，键为分组名称，值为倍率"}, "save": "保存倍率设置"}, "chatLinkSettings": {"title": "聊天链接设置", "info": "配置聊天链接，该配置在令牌中的聊天生效以及首页的Playground中的聊天生效. <br />链接中可以使&#123;key&#125;替换用户的令牌，&#123;server&#125;替换服务器地址。例如：{'https://chat.oneapi.pro/#/?settings={\"key\":\"sk-{key}\",\"url\":\"{server}\"}'}<br />如果未配置，会默认配置以下4个链接：<br />ChatGPT Next ： {'https://chat.oneapi.pro/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />chatgpt-web-midjourney-proxy ： {'https://vercel.ddaiai.com/#/?settings={\"key\":\"{key}\",\"url\":\"{server}\"}'}<br />AMA 问天 ： {'ama://set-api-key?server={server}&key={key}'}<br />opencat ： {'opencat://team/join?domain={server}&token={key}'}<br />排序规则：值越大越靠前，值相同则按照配置顺序", "save": "保存聊天链接设置"}, "audioTokenSettings": {"title": "音频Token设置", "info": "配置音频倍率. 配置格式为JSON，键为模型名称，值为输入输出Token倍率。例如：{\"gpt-4o-audio-preview\":{\"input_audio_tokens_ratio\":40,\"output_audio_tokens_ratio\":20},\"gpt-4o-mini-audio-preview\":{\"input_audio_tokens_ratio\":67,\"output_audio_tokens_ratio\":34}}", "save": "保存音频Token设置"}, "disableChannelKeywordsSettings": {"title": "禁用通道关键词设置", "info": "配置禁用通道关键词，每行一个关键词。", "save": "保存禁用通道关键词设置"}}, "systemSettings": {"title": "系统设置", "generalSettings": {"title": "系统设置", "serverAddress": "服务器地址", "serverAddressPlaceholder": "例如：https://yourdomain.com", "updateServerAddress": "更新服务器地址"}, "configureLoginRegister": {"title": "配置登录和注册", "passwordLogin": "允许通过密码进行登录", "passwordRegister": "允许通过密码进行注册", "emailVerification": "通过密码注册时需要进行邮箱验证", "gitHubOAuth": "允许通过 GitHub 账户登录 & 注册", "oidcAuth": "允许通过 OIDC 账户登录 & 注册", "weChatAuth": "允许通过微信登录 & 注册", "larkAuth": "允许通过飞书登录 & 注册", "registerEnabled": "允许新用户注册（此项为否时，新用户将无法以任何方式进行注册）", "turnstileCheck": "启用 Turnstile 用户校验", "gitHubOldIdClose": "关闭 GitHub 老 ID 登录"}, "configureEmailDomainWhitelist": {"title": "配置邮箱域名白名单", "subTitle": "用以防止恶意用户利用临时邮箱批量注册", "emailDomainRestriction": "启用邮箱域名白名单", "allowedEmailDomains": "允许的邮箱域名", "save": "保存邮箱域名白名单设置"}, "configureSMTP": {"title": "配置SMTP", "subTitle": "用以支持系统的邮件发送", "alert": "请注意，有些邮箱服务商发送邮件时会携带你的服务器IP地址，非个人使用时建议使用专业的邮件服务商", "smtpServer": "SMTP 服务器地址", "smtpServerPlaceholder": "例如：smtp.qq.com", "smtpPort": "SMTP 端口", "smtpPortPlaceholder": "默认: 587", "smtpAccount": "SMTP 账户", "smtpAccountPlaceholder": "通常是邮箱地址", "smtpFrom": "SMTP 发送者邮箱", "smtpFromPlaceholder": "通常和邮箱地址保持一致", "smtpToken": "SMTP 访问凭证", "smtpTokenPlaceholder": "敏感信息不会发送到前端显示", "save": "保存 SMTP 设置"}, "configureGitHubOAuthApp": {"title": "配置 GitHub OAuth 应用", "subTitle": "用以支持通过 GitHub 进行登录注册，", "manageLink": "点击此处", "manage": "管理你的 GitHub OAuth App", "alert1": "Homepage URL 填", "alert2": "，Authorization callback URL 填", "clientId": "GitHub Client ID", "clientSecret": "GitHub Client Secret", "clientIdPlaceholder": "输入你注册的 GitHub OAuth APP 的 ID", "clientSecretPlaceholder": "敏感信息不会发送到前端显示", "saveButton": "保存 GitHub OAuth 设置"}, "configureWeChatServer": {"title": "配置 WeChat 服务器", "subTitle": "用以支持通过微信进行登录注册，", "learnLink": "点击此处", "learn": "了解 WeChat Server", "serverAddress": "WeChat Server 服务器地址", "serverAddressPlaceholder": "例如：https://yourdomain.com", "accessToken": "WeChat Server 访问凭证", "accessTokenPlaceholder": "敏感信息不会发送到前端显示", "qrCodeImage": "微信公众号二维码图片链接", "qrCodeImagePlaceholder": "输入一个图片链接", "saveButton": "保存 WeChat Server 设置"}, "configureFeishuAuthorization": {"title": "配置飞书授权", "subTitle": "用以支持通过飞书进行登录注册，", "manageLink": "点击此处", "manage": "管理你的飞书应用", "alert1": "主页链接填", "alert2": "，重定向 URL 填 ", "appId": "App ID", "appIdPlaceholder": "输入 App ID", "appSecret": "App Secret", "appSecretPlaceholder": "敏感信息不会发送到前端显示", "saveButton": "保存飞书 OAuth 设置"}, "configureOIDCAuthorization": {"title": "配置OIDC统一授权系统", "subTitle": "用以配置标准OIDC授权登录系统", "alert1": "主页链接填", "alert2": "，重定向 URL 填 ", "clientId": "客户端ID（Client ID）", "clientIdPlaceholder": "请输入客户端ID", "clientSecret": "客户端密钥(Secret)", "clientSecretPlaceholder": "敏感信息不会发送到前端显示", "issuer": "OIDC发行者(Issuer)", "issuerPlaceholder": "请输入OIDC发行者", "scopes": "权限范围（Sc<PERSON><PERSON>）", "scopesPlaceholder": "请输入权限范围（用英文逗号分隔）,通常为'openid,email,profile'", "usernameClaims": "用户名声明（Claims）", "usernameClaimsPlaceholder": "请输入用户名声明(例如username)", "saveButton": "保存OIDC设置"}, "configureTurnstile": {"title": "配置 Turnstile", "subTitle": "用以支持用户校验，", "manageLink": "点击此处", "manage": "管理你的 Turnstile Sites，推荐选择 Invisible Widget Type", "siteKey": "Turnstile Site Key", "siteKeyPlaceholder": "输入你注册的 Turnstile Site Key", "secretKey": "Turnstile Secret Key", "secretKeyPlaceholder": "敏感信息不会发送到前端显示", "saveButton": "保存 Turnstile 设置"}}, "otherSettings": {"title": "其他设置", "generalSettings": {"title": "通用设置", "currentVersion": "当前版本", "checkUpdate": "检查更新", "noticeLabel": "公告", "noticePlaceholder": "在此输入新的公告内容，支持 Markdown & HTML 代码", "saveNotice": "保存公告"}, "customSettings": {"title": "个性化设置", "systemNameLabel": "系统名称", "systemNamePlaceholder": "在此输入系统名称", "setSystemName": "设置系统名称", "logoLabel": "Logo 图片地址", "logoPlaceholder": "在此输入Logo 图片地址", "setLogo": "设置 Logo", "homePageContentLabel": "首页内容", "homePageContentPlaceholder": "在此输入首页内容，支持 Markdown & HTML 代码，设置后首页的状态信息将不再显示。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为首页。", "saveHomePageContent": "保存首页内容", "aboutLabel": "关于", "aboutPlaceholder": "在此输入新的关于内容，支持 Markdown & HTML 代码。如果输入的是一个链接，则会使用该链接作为 iframe 的 src 属性，这允许你设置任意网页作为关于页面。", "saveAbout": "保存关于", "copyrightWarning": "移除 One Hub 的版权标识必须首先获得授权，项目维护需要花费大量精力，如果本项目对你有意义，请主动支持本项目。", "footerLabel": "页脚设置", "footerPlaceholder": "在此输入新的页脚，留空则使用默认页脚，支持 HTML 代码", "setFooter": "设置页脚"}, "updateDialog": {"newVersion": "新版本", "close": "关闭", "viewGitHub": "去GitHub查看"}}}, "telegramPage": {"title": "Telegram Bot菜单", "createMenu": "新建", "reloadMenu": "重新载入菜单", "searchPlaceholder": "搜索ID和命令...", "refresh": "刷新", "online": "在线", "offline": "离线", "reloadSuccess": "重载成功！", "operationSuccess": "操作成功完成！", "infoMessage": "添加修改菜单命令/说明后（如果没有修改命令和说明可以不用重载），需要重新载入菜单才能生效。如果未查看到新菜单，请尝试杀后台后重新启动程序。", "id": "ID", "command": "命令", "description": "说明", "replyType": "回复类型", "replyContent": "回复内容", "action": "操作"}, "login": {"usernameOrEmail": "用户名/邮箱", "password": "密码", "usernameRequired": "用户名/邮箱是必填项", "passwordRequired": "密码是必填项", "forgetPassword": "忘记密码?", "wechatVerificationCodeLogin": "微信验证码登录", "wechatLoginInfo": "请使用微信扫描二维码关注公众号，输入「验证码」获取验证码（三分钟内有效）", "qrCode": "二维码", "codeRequired": "验证码不能为空", "githubError": "操作失败，重定向至登录界面中...", "githubCountError": "出现错误，第 {{count}} 次重试中...", "githubLogin": "GitHub 登录", "oidcError": "操作失败，重定向至登录界面中...", "oidcCountError": "出现错误，第 {{count}} 次重试中...", "oidcLogin": "OIDC 登录", "larkLogin": "飞书 登录", "passwordRest": "密码重置确认", "useGithubLogin": "使用 Github 登录", "useWechatLogin": "使用 Wechat 登录", "useOIDCLogin": "使用 OIDC 登录", "useLarkLogin": "使用飞书登录"}, "description": "All in one 的 OpenAI 接口\n整合各种 API 访问方式\n一键部署，开箱即用", "about": {"loadingError": "加载关于内容失败...", "aboutTitle": "关于", "aboutDescription": "可在设置页面设置关于内容，支持 HTML & Markdown", "projectRepo": "项目仓库地址："}, "footer": {"developedBy": "由", "basedOn": "基于", "sourceCode": "源代码遵循", "license": "MIT 协议"}, "CheckUpdatesTable": {"checkUpdates": "检查更新", "url": "URL", "fetchData": "获取数据", "noUpdates": "无更新", "newModels": "新增模型", "priceChangeModels": "价格变动模型(仅供参考，如果你自己修改了对应模型的价格请忽略)", "note": "注意", "overwriteOrAddOnly": "你可以选择覆盖或者仅添加新增，如果你选择覆盖，将会删除你自己添加的模型价格，完全使用远程配置，如果你选择仅添加新增，将会只会添加 新增模型的价格", "overwriteData": "覆盖数据", "addNewOnly": "仅添加新增", "cancel": "取消", "dataFormatIncorrect": "数据格式不正确", "pleaseFetchData": "请先获取数据", "noNewModels": "没有新增模型", "operationCompleted": "操作成功完成！", "inputMultiplierChanged": "输入倍率由", "to": "变为", "outputMultiplierChanged": "输出倍率由"}, "error": {"unknownError": "未知错误"}, "common": {"submit": "提交", "cancel": "取消", "delete": "删除", "verificationCode": "验证码", "noData": "暂无数据", "edit": "编辑", "create": "创建", "unknown": "未知", "close": "关闭", "saveSuccess": "保存成功！", "unableServer": "无法正常连接至服务器！", "unableServerTip": "新版本可用：{{version}}，请使用快捷键 Shift + F5 刷新页面", "bindOk": "绑定成功！", "loginOk": "登录成功！", "registerOk": "注册成功！", "registerTip": "验证码发送成功，请检查你的邮箱！", "processing": "处理中...", "serverError": "服务器错误", "again": "重试 ({{count}})", "back": "返回", "none": "无", "show": "显示", "copyUrl": "复制地址", "downImg": "下载图片", "newWindos": "新窗口打开", "imgUrl": "图片地址", "link": "链接", "enable": "已启用", "disable": "已禁用", "expired": "已过期", "exhaust": "已耗尽", "execute": "是否执行 {{title}}?", "executeConfirm": "执行", "deleteConfirm": "是否删除 {{title}}?", "deleteSuccess": "删除成功！", "deleteError": "删除失败: {{message}}"}, "channel_row": {"priorityTip": "优先级不能小于 0", "weightTip": "权重不能小于 1", "modelTestTip": "请先设置测试模型", "modelTestSuccess": "通道 {{channel}}: {{model}} 测试成功，耗时 {{time}} 秒。", "clickUpdateQuota": "点击更新余额", "onlyChat": "仅支持chat模型", "test": "测试", "channelWeb": "官网", "delTag": "删除标签", "canModels": "可用模型:", "testModels": "测速模型", "proxy": "代理地址:", "otherArg": "其他参数", "delChannel": "删除渠道", "delChannelInfo": "是否删除渠道", "check": "检测", "manual": "手动", "auto": "自动", "testAllChannel": "已成功开始测试所有通道，请刷新页面查看结果。", "updateChannelBalance": "已更新完毕所有已启用通道余额！", "delChannelCount": "已删除所有禁用渠道，共计 {{count}} 个", "updateOk": "更新成功！", "delTagInfo1": "是否删除标签", "delTagInfo2": "⚠️ 注意：该操作会删除渠道。", "batchDelete": "批量删除", "batchDeleteTip": "是否批量删除所选渠道？", "batchDeleteSuccess": "批量删除成功！", "batchDeleteError": "批量删除失败: {{message}}", "batchDeleteErrorTip": "批量删除出错: {{message}}", "batchAddIDRequired": "请至少选择一个渠道", "getTagChannelsError": "获取标签渠道失败: {{message}}", "getTagChannelsErrorTip": "获取标签渠道出错: {{message}}", "tag": "标签", "enableAllChannels": "启用所有渠道", "disableAllChannels": "禁用所有渠道", "priorityUpdateSuccess": "优先级更新成功", "priorityUpdateError": "优先级更新失败: {{message}}", "weightUpdateSuccess": "权重更新成功", "weightUpdateError": "权重更新失败: {{message}}", "deleteTagAndChannels": "删除标签及其所有渠道", "tagChannelList": "标签渠道列表", "refreshList": "刷新列表", "noTagChannels": "没有找到标签渠道", "deleteTag": "删除标签", "deleteTagConfirm": "确定要删除标签 {{tag}} 及其所有渠道吗？此操作不可恢复。", "deleteTagSuccess": "标签 {{tag}} 删除成功", "deleteTagError": "删除标签失败: {{message}}", "enableTagChannels": "启用标签渠道", "disableTagChannels": "禁用标签渠道", "tagChannelsConfirm": "确定要{{action}}标签 {{tag}} 下的所有渠道吗？", "enable": "启用", "disable": "禁用", "tagChannelsSuccess": "标签渠道{{action}}成功", "tagChannelsError": "标签渠道{{action}}失败: {{message}}", "batchDeleteConfirm": "确定要删除选中的 {{count}} 个渠道吗？此操作不可恢复。"}, "validation": {"requiredName": "名称 不能为空"}, "payment_edit": {"requiredIcon": "图标 不能为空", "requiredFixedFee": "固定手续费 不能小于 0", "requiredPercentFee": "百分比手续费 不能小于 0", "requiredCurrency": "货币 不能为空", "updateOk": "更新成功！", "addOk": "创建成功！", "paymentEdit": "编辑支付", "paymentType": "支付类型", "notifyDomain": "回调域名", "notifyDomainTip": "支付回调的域名，除非你自行配置过，否则保持为空", "FixedTip": "每次支付收取固定的手续费，单位 美元", "percentTip": "每次支付按百分比收取手续费，如果为5%，请填写 0.05", "currencyType": "网关货币类型", "currencyTip": "该网关是收取什么货币的，请查询对应网关文档"}, "payment_row": {"sortTip": "排序不能小于 0", "delPayment": "删除支付", "delPaymentTip": "是否删除支付"}, "pricing_edit": {"typeErr": "类型 错误", "requiredType": "类型 不能为空", "channelTypeErr": "渠道类型 错误", "requiredChannelType": "渠道类型 不能为空", "requiredInput": "输入倍率 不能为空", "requiredOutput": "输出倍率 不能为空", "requiredModels": "模型 不能为空", "name": "名称", "type": "类型", "channelType": "渠道类型", "model": "模型", "modelTip": "请选择该价格所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的", "requiredModelName": "模型名称不能为空", "typeCheck": "类型只能是tokens或times", "channelTypeErr2": "所属渠道类型错误", "modelNameRe": "模型名称不能重复", "inputVal": "输入倍率必须大于等于0", "outputVal": "输出倍率必须大于等于0", "saveOk": "保存成功", "delTip": "确定删除?", "delInfoTip": "确定删除 {{name}} 吗？", "delGroup": "删除价格组", "delGroupTip": "是否删除价格组？"}, "redemption_edit": {"requiredQuota": "必须大于等于0", "requiredCount": "必须大于等于1", "addOk": "兑换码创建成功！", "editOk": "兑换码更新成功！", "number": "数量"}, "telegram_edit": {"requiredCommand": "命令 不能为空", "requiredDes": "说明 不能为空", "requiredParseMode": "消息类型 不能为空", "requiredMes": "消息内容 不能为空", "updateOk": "菜单更新成功！", "addOk": "菜单创建成功！", "msgType": "消息类型", "msgInfo": "消息内容"}, "ui-component": {"allModels": "查看全部模型", "modelName": "模型名称"}, "auth": {"restPassword": "密码重置", "newPassword": "新密码", "invalidLink": "无效的链接", "newPasswordInfo": "你的新密码是:", "newPasswordEdit": "请登录后及时修改密码", "restPasswordClick": "点击重置密码"}, "res_time": {"second": "秒", "testClick": "点击测速(仅支持chat模型)", "lastTime": "上次测速时间：", "noTest": "未测试"}, "home": {"loadingErr": "加载首页内容失败..."}, "jump": "正在跳转中...", "渠道名称": "渠道名称", "渠道类型": "渠道类型", "渠道API地址": "渠道API地址", "密钥": "密钥", "其他参数": "其他参数", "代理地址": "代理地址", "测速模型": "测速模型", "模型": "模型", "模型映射关系": "模型映射关系", "用户组": "用户组", "仅支持聊天": "仅支持聊天", "标签": "标签", "请选择渠道类型": "请选择渠道类型", "请为渠道命名": "请为渠道命名", "可空，请输入中转API地址，例如通过cloudflare中转": "可空，请输入中转API地址，例如通过cloudflare中转", "请输入渠道对应的鉴权密钥": "请输入渠道对应的鉴权密钥", "单独设置代理地址，支持http和socks5，例如：http://127.0.0.1:1080,代理地址中可以通过 `%s` 作为会话标识占位符，程序中检测到有占位符会根据Key生成唯一会话标识符进行替换": "单独设置代理地址，支持http和socks5，例如：http://127.0.0.1:1080,代理地址中可以通过 `%s` 作为会话标识占位符，程序中检测到有占位符会根据Key生成唯一会话标识符进行替换", "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型": "用于测试使用的模型，为空时无法测速,如：gpt-3.5-turbo，仅支持chat模型", "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的": "请选择该渠道所支持的模型,你也可以输入通配符*来匹配模型，例如：gpt-3.5*，表示支持所有gpt-3.5开头的模型，*号只能在最后一位使用，前面必须有字符，例如：gpt-3.5*是正确的，*gpt-3.5是错误的", "模型映射关系：例如用户请求A模型，实际转发给渠道的模型为B。在B模型加前缀+，表示使用传入模型计费，例如：+gpt-3.5-turbo": "模型映射关系：例如用户请求A模型，实际转发给渠道的模型为B。在B模型加前缀+，表示使用传入模型计费，例如：+gpt-3.5-turbo", "请选择该渠道所支持的用户组": "请选择该渠道所支持的用户组", "如果选择了仅支持聊天，那么遇到有函数调用的请求会跳过该渠道": "如果选择了仅支持聊天，那么遇到有函数调用的请求会跳过该渠道", "必须填写所有数据后才能获取模型列表": "必须填写所有数据后才能获取模型列表", "你可以为你的渠道打一个标签，打完标签后，可以通过标签进行批量管理渠道，注意：设置标签后某些设置只能通过渠道标签修改，无法在渠道列表中修改。": "你可以为你的渠道打一个标签，打完标签后，可以通过标签进行批量管理渠道，注意：设置标签后某些设置只能通过渠道标签修改，无法在渠道列表中修改。", "从OpenAI获取模型列表": "从OpenAI获取模型列表", "从渠道获取模型列表": "从渠道获取模型列表", "替换 API 版本": "替换 API 版本", "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx.com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable": "输入后，会替换请求地址中的v1，例如：freeapi，则请求chat时会变成https://xxx.com/freeapi/chat/completions,如果需要禁用版本号，请输入 disable", "AZURE_OPENAI_ENDPOINT": "AZURE_OPENAI_ENDPOINT", "默认 API 版本": "默认 API 版本", "请填写AZURE_OPENAI_ENDPOINT": "请填写AZURE_OPENAI_ENDPOINT", "请输入默认API版本，例如：2024-05-01-preview": "请输入默认API版本，例如：2024-05-01-preview", "按照如下格式输入：APIKey|SecretKey": "按照如下格式输入：APIKey|SecretKey", "插件参数": "插件参数", "请输入插件参数，即 X-DashScope-Plugin 请求头的取值": "请输入插件参数，即 X-DashScope-Plugin 请求头的取值", "版本号": "版本号", "按照如下格式输入：APPID|APISecret|APIKey": "按照如下格式输入：APPID|APISecret|APIKey", "请输入版本号，例如：v3.1": "请输入版本号，例如：v3.1", "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041": "按照如下格式输入：APIKey-AppId，例如：fastgpt-0sp2gtvfdgyi4k30jwlgwf1i-64f335d84283f05518e9e041", "按照如下格式输入：AppId|SecretId|SecretKey": "按照如下格式输入：AppId|SecretId|SecretKey", "从Gemini获取模型列表": "从Gemini获取模型列表", "请输入版本号，例如：v1": "请输入版本号，例如：v1", "位置/区域": "位置/区域", "请输入你 Speech Studio 的位置/区域，例如：eastasia": "请输入你 Speech Studio 的位置/区域，例如：eastasia", "按照如下格式输入：APISecret|groupID": "按照如下格式输入：APISecret|groupID", "从Deepseek获取模型列表": "从Deepseek获取模型列表", "从Moonshot获取模型列表": "从Moonshot获取模型列表", "从Mistral获取模型列表": "从Mistral获取模型列表", "从Groq获取模型列表": "从Groq获取模型列表", "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空": "按照如下格式输入：Region|AccessKeyID|SecretAccessKey|SessionToken 其中SessionToken可不填空", "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填": "密钥填写midjourney-proxy的密钥，如果没有设置密钥，可以随便填", "地址填写midjourney-proxy部署的地址": "地址填写midjourney-proxy部署的地址", "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN": "按照如下格式输入：CLOUDFLARE_ACCOUNT_ID|CLOUDFLARE_API_TOKEN", "从Cohere获取模型列表": "从Cohere获取模型列表", "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型": "模型名称为coze-{bot_id}，你也可以直接使用 coze-* 通配符来匹配所有coze开头的模型", "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"},注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。": "模型名称映射， 你可以取一个容易记忆的名字来代替coze-{bot_id}，例如：{\"coze-translate\": \"coze-xxxxx\"},注意：如果使用了模型映射，那么上面的模型名称必须使用映射前的名称，上述例子中，你应该在模型中填入coze-translate(如果已经使用了coze-*，可以忽略)。", "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息": "请输入你部署的Ollama地址，例如：http://127.0.0.1:11434，如果你使用了cloudflare Zero Trust，可以在下方插件填入授权信息", "请随意填写": "请随意填写", "按照如下格式输入：SecretId|SecretKey": "按照如下格式输入：SecretId|SecretKey", "密钥填写Suno-API的密钥，如果没有设置密钥，可以随便填": "密钥填写Suno-API的密钥，如果没有设置密钥，可以随便填", "地址填写Suno-API部署的地址": "地址填写Suno-API部署的地址", "请参考wiki中的文档获取key. https://github.com/MartialBE/one-hub/wiki/VertexAI": "请参考wiki中的文档获取key. https://github.com/MartialBE/one-hub/wiki/VertexAI", "知识库": "知识库", "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。": "请前往开放平台的知识库上传文档，然后使用知识库功能进行检索。", "知识库ID": "知识库ID", "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)": "当涉及到知识库ID时，请前往开放平台的知识库模块进行创建或获取(是知识库ID不是文档ID！)", "知识库模板": "知识库模板", "请求模型时的知识库模板, 请查看文档填写，否则不用填写": "请求模型时的知识库模板, 请查看文档填写，否则不用填写", "网页搜索": "网页搜索", "使用网页搜索功能，对用户输入的内容进行搜索": "使用网页搜索功能，对用户输入的内容进行搜索", "启用": "启用", "是否启用网页搜索": "是否启用网页搜索", "声音映射": "声音映射", "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy": "将OpenAI的声音角色映射到azure的声音角色, 如果有role，请用|隔开，例如zh-CN-YunxiNeural|boy", "alloy 映射": "alloy 映射", "默认 zh-CN-YunxiNeural": "默认 zh-CN-YunxiNeural", "echo 映射": "echo 映射", "默认 zh-CN-YunyangNeural": "默认 zh-CN-YunyangNeural", "fable 映射": "fable 映射", "默认 zh-CN-YunxiNeural|boy": "默认 zh-CN-YunxiNeural|boy", "onyx 映射": "onyx 映射", "默认 zh-CN-YunyeNeural": "默认 zh-CN-YunyeNeural", "nova 映射": "nova 映射", "默认 zh-CN-XiaochenNeural": "默认 zh-C<PERSON>-XiaochenNeural", "shimmer 映射": "shimmer 映射", "默认 zh-CN-XiaohanNeural": "默认 zh-<PERSON><PERSON>-XiaohanNeural", "Header 配置": "Header 配置", "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header": "本配置主要是用于使用cloudflare Zero Trust将端口暴露到公网时，需要配置的header", "代码执行": "代码执行", "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启": "使用代码执行功能，开启后，计算tokens不准确，建议个人使用开启", "是否启用代码执行": "是否启用代码执行", "taskPage": {"task": "任务ID", "subTime": "提交时间", "finishTime": "完成时间", "channel": "渠道", "user": "用户", "platform": "平台", "type": "类型", "time": "耗时", "progress": "进度", "status": "任务状态(点击查看结果)", "fail": "失败原因"}, "suno": {"music": "音频", "video": "视频", "lyrics": "歌词", "response": "响应体"}, "预计费选项": "预计费选项", "这里选择预计费选项，用于预估费用，如果你觉得计算图片占用太多资源，可以选择关闭图片计费。但是请注意：有些渠道在stream下是不会返回tokens的，这会导致输入tokens计算错误。": "这里选择预计费选项，用于预估费用，如果你觉得计算图片占用太多资源，可以选择关闭图片计费。但是请注意：有些渠道在stream下是不会返回tokens的，这会导致输入tokens计算错误。", "userGroup": {"title": "用户分组", "create": "新建分组", "id": "ID", "symbol": "标识", "name": "名称", "ratio": "倍率", "public": "是否公开", "enable": "是否启用", "symbolTip": "标识用于区分用户组,请使用英文，不可重复", "nameTip": "给用户看的名称", "apiRate": "API速率", "apiRateTip": "每分钟允许的请求数,当速率小于60时，使用计数器限制器，当速率大于等于60时，使用令牌桶限制器，仅在启用Redis时有效"}, "modelOwnedby": {"title": "模型归属", "create": "新建模型归属", "id": "ID", "name": "名称", "icon": "图标", "iconTip": "图标用于在模型列表中显示", "action": "操作", "idTip": "渠道ID,数字，请设置 大于1000以上的数字，设置好不可更改", "nameTip": "渠道名称"}}